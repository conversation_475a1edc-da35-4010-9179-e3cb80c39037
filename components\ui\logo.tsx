'use client';

import React from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  showText?: boolean;
  className?: string;
  onClick?: () => void;
  variant?: 'default' | 'stacked' | 'minimal';
}

const sizeConfig = {
  sm: {
    container: 'h-8',
    image: 'h-6 w-6',
    text: 'text-sm',
    spacing: 'space-x-1.5'
  },
  md: {
    container: 'h-10',
    image: 'h-8 w-8',
    text: 'text-base',
    spacing: 'space-x-2'
  },
  lg: {
    container: 'h-12',
    image: 'h-10 w-10',
    text: 'text-lg',
    spacing: 'space-x-2.5'
  },
  xl: {
    container: 'h-16',
    image: 'h-12 w-12',
    text: 'text-xl',
    spacing: 'space-x-3'
  }
};

export function Logo({ 
  size = 'md', 
  showText = true, 
  className,
  onClick 
}: LogoProps) {
  const config = sizeConfig[size];
  
  const logoContent = (
    <motion.div 
      className={cn(
        'flex items-center cursor-pointer select-none',
        config.container,
        config.spacing,
        className
      )}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      transition={{ duration: 0.2, ease: "easeInOut" }}
      onClick={onClick}
    >
      {/* Logo Image */}
      <motion.div 
        className={cn(
          'relative flex-shrink-0 rounded-lg overflow-hidden shadow-sm',
          config.image
        )}
        whileHover={{ rotate: [0, -5, 5, 0] }}
        transition={{ duration: 0.6, ease: "easeInOut" }}
      >
        <Image
          src="/assets/logo.png"
          alt="Money Fast Funding Logo"
          fill
          className="object-contain"
          priority
        />
      </motion.div>

      {/* Logo Text */}
      {showText && (
        <motion.div 
          className="flex flex-col leading-none"
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <span className={cn(
            'font-bold bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 dark:from-blue-400 dark:via-blue-500 dark:to-blue-600 bg-clip-text text-transparent',
            config.text
          )}>
            Money Fast
          </span>
          <span className={cn(
            'font-semibold text-amber-600 dark:text-amber-400 -mt-0.5',
            size === 'sm' ? 'text-xs' : 
            size === 'md' ? 'text-sm' : 
            size === 'lg' ? 'text-base' : 'text-lg'
          )}>
            Funding
          </span>
        </motion.div>
      )}
    </motion.div>
  );

  return logoContent;
}

// Responsive Logo that adapts to screen size
export function ResponsiveLogo({ 
  className,
  onClick,
  showText = true 
}: Omit<LogoProps, 'size'>) {
  return (
    <div className={className}>
      {/* Mobile - Small */}
      <div className="block sm:hidden">
        <Logo size="sm" showText={showText} onClick={onClick} />
      </div>
      
      {/* Tablet - Medium */}
      <div className="hidden sm:block lg:hidden">
        <Logo size="md" showText={showText} onClick={onClick} />
      </div>
      
      {/* Desktop - Large */}
      <div className="hidden lg:block">
        <Logo size="lg" showText={showText} onClick={onClick} />
      </div>
    </div>
  );
}
