'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Paperclip, Send } from 'lucide-react';
import { useLanguage } from '@/components/language-provider';

export function ContactSection() {
  const { t } = useLanguage();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    amount: '',
    statements: '',
    attachments: null as FileList | null
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, attachments: e.target.files }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Create FormData for file upload
    const submitData = new FormData();
    
    // Append string fields explicitly
    submitData.append('name', formData.name);
    submitData.append('email', formData.email);
    submitData.append('phone', formData.phone);
    submitData.append('amount', formData.amount);
    submitData.append('statements', formData.statements);
    
    // Handle attachments separately
    if (formData.attachments) {
      Array.from(formData.attachments).forEach(file => {
        submitData.append('attachments', file);
      });
    }

    try {
      // This would be replaced with your actual endpoint
      const response = await fetch(process.env.NEXT_PUBLIC_FORM_ENDPOINT || '/api/contact', {
        method: 'POST',
        body: submitData,
      });

      if (response.ok) {
        alert('Application submitted successfully!');
        setFormData({
          name: '',
          email: '',
          phone: '',
          amount: '',
          statements: '',
          attachments: null
        });
      } else {
        alert('Error submitting application. Please try again.');
      }
    } catch (error) {
      console.error('Error:', error);
      alert('Error submitting application. Please try again.');
    }
  };

  return (
    <section id="contact" className="py-20 bg-gradient-to-b from-background to-blue-50/50 dark:to-blue-950/20">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          <Card className="border-0 shadow-2xl bg-white/95 dark:bg-card/95 backdrop-blur-sm">
            <CardHeader className="text-center pb-8">
              <CardTitle className="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">
                {t.contact.title}
              </CardTitle>
            </CardHeader>
            <CardContent className="p-8">
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Input
                      name="name"
                      placeholder={t.contact.form.name}
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      className="h-12 text-base"
                    />
                  </div>
                  <div>
                    <Input
                      name="email"
                      type="email"
                      placeholder={t.contact.form.email}
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className="h-12 text-base"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Input
                      name="phone"
                      type="tel"
                      placeholder={t.contact.form.phone}
                      value={formData.phone}
                      onChange={handleInputChange}
                      required
                      className="h-12 text-base"
                    />
                  </div>
                  <div>
                    <Input
                      name="amount"
                      placeholder={t.contact.form.amount}
                      value={formData.amount}
                      onChange={handleInputChange}
                      required
                      className="h-12 text-base"
                    />
                  </div>
                </div>

                <div>
                  <Textarea
                    name="statements"
                    placeholder={t.contact.form.statements}
                    value={formData.statements}
                    onChange={handleInputChange}
                    required
                    className="min-h-[120px] text-base resize-none"
                  />
                </div>

                <div>
                  <div className="flex items-center space-x-4">
                    <label htmlFor="file-upload" className="cursor-pointer flex items-center space-x-2 text-blue-600 hover:text-blue-700 transition-colors">
                      <Paperclip className="h-5 w-5" />
                      <span className="text-sm font-medium">{t.contact.form.attachFiles}</span>
                    </label>
                    <input
                      id="file-upload"
                      type="file"
                      multiple
                      onChange={handleFileChange}
                      className="hidden"
                      accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                    />
                    {formData.attachments && (
                      <span className="text-sm text-muted-foreground">
                        {t.contact.form.attachments} ({formData.attachments.length})
                      </span>
                    )}
                  </div>
                </div>

                <div className="text-center">
                  <Button 
                    type="submit" 
                    size="lg"
                    className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-12 py-6 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                  >
                    {t.contact.form.send}
                    <Send className="ml-2 h-5 w-5" />
                  </Button>
                </div>

                <p className="text-xs text-muted-foreground text-center mt-4">
                  {t.contact.form.privacy}
                </p>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}