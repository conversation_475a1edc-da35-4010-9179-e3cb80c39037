'use client';

import React, { useEffect, useState, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowRight, CheckCircle, DollarSign, TrendingUp, Clock } from 'lucide-react';
import { useLanguage } from '@/components/language-provider';
import { motion } from 'framer-motion';

// Enhanced Background Components
const FloatingOrbs = React.memo(() => {
  const orbs = useMemo(() =>
    Array.from({ length: 6 }, (_, i) => ({
      id: i,
      size: Math.random() * 300 + 100,
      x: Math.random() * 100,
      y: Math.random() * 100,
      duration: Math.random() * 10 + 15,
      delay: Math.random() * 5,
    })), []
  );

  return (
    <div className="absolute inset-0 overflow-hidden">
      {orbs.map((orb) => (
        <motion.div
          key={orb.id}
          className="absolute rounded-full opacity-20 blur-xl"
          style={{
            width: orb.size,
            height: orb.size,
            left: `${orb.x}%`,
            top: `${orb.y}%`,
            background: `linear-gradient(45deg,
              hsl(${210 + orb.id * 30}, 70%, 60%),
              hsl(${240 + orb.id * 20}, 80%, 70%)
            )`,
          }}
          animate={{
            x: [0, 50, -30, 0],
            y: [0, -30, 20, 0],
            scale: [1, 1.2, 0.8, 1],
          }}
          transition={{
            duration: orb.duration,
            delay: orb.delay,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
      ))}
    </div>
  );
});

const GeometricParticles = React.memo(() => {
  const particles = useMemo(() =>
    Array.from({ length: 15 }, (_, i) => ({
      id: i,
      size: Math.random() * 20 + 10,
      x: Math.random() * 100,
      y: Math.random() * 100,
      rotation: Math.random() * 360,
      duration: Math.random() * 8 + 12,
      delay: Math.random() * 3,
    })), []
  );

  return (
    <div className="absolute inset-0 overflow-hidden">
      {particles.map((particle) => (
        <motion.div
          key={particle.id}
          className="absolute opacity-30"
          style={{
            width: particle.size,
            height: particle.size,
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            rotate: particle.rotation,
          }}
          animate={{
            y: [0, -100, 0],
            x: [0, 50, -20, 0],
            rotate: [0, 180, 360],
            opacity: [0.3, 0.7, 0.3],
          }}
          transition={{
            duration: particle.duration,
            delay: particle.delay,
            repeat: Infinity,
            ease: "linear",
          }}
        >
          {particle.id % 3 === 0 ? (
            <div className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 dark:from-blue-300 dark:to-purple-400 rounded-full" />
          ) : particle.id % 3 === 1 ? (
            <div className="w-full h-full bg-gradient-to-br from-amber-400 to-orange-500 dark:from-amber-300 dark:to-orange-400 transform rotate-45" />
          ) : (
            <div className="w-full h-full bg-gradient-to-br from-emerald-400 to-teal-500 dark:from-emerald-300 dark:to-teal-400 clip-path-triangle"
                 style={{ clipPath: 'polygon(50% 0%, 0% 100%, 100% 100%)' }} />
          )}
        </motion.div>
      ))}
    </div>
  );
});

const LiquidBlobs = React.memo(() => {
  const blobs = useMemo(() =>
    Array.from({ length: 4 }, (_, i) => ({
      id: i,
      size: Math.random() * 200 + 150,
      x: Math.random() * 80 + 10,
      y: Math.random() * 80 + 10,
      duration: Math.random() * 15 + 20,
    })), []
  );

  return (
    <div className="absolute inset-0 overflow-hidden">
      {blobs.map((blob) => (
        <motion.div
          key={blob.id}
          className="absolute opacity-10 animate-morph"
          style={{
            width: blob.size,
            height: blob.size,
            left: `${blob.x}%`,
            top: `${blob.y}%`,
            background: `linear-gradient(45deg,
              hsl(${200 + blob.id * 40}, 60%, 50%),
              hsl(${260 + blob.id * 30}, 70%, 60%)
            )`,
            filter: 'blur(40px)',
          }}
          animate={{
            x: [0, 100, -50, 0],
            y: [0, -50, 80, 0],
            scale: [1, 1.3, 0.7, 1],
          }}
          transition={{
            duration: blob.duration,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
      ))}
    </div>
  );
});

interface HeroSectionProps {
  animationStyle?: 'orbs' | 'particles' | 'blobs' | 'all';
}

export function HeroSection({ animationStyle = 'all' }: HeroSectionProps) {
  const { t } = useLanguage();
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const scrollToContact = () => {
    const element = document.querySelector('#contact');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="home" className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Enhanced 3D Gradient Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-amber-50 dark:from-blue-950 dark:via-slate-900 dark:to-amber-950" />

      {/* Dynamic Background Animations */}
      {(animationStyle === 'orbs' || animationStyle === 'all') && <FloatingOrbs />}
      {(animationStyle === 'particles' || animationStyle === 'all') && <GeometricParticles />}
      {(animationStyle === 'blobs' || animationStyle === 'all') && <LiquidBlobs />}

      {/* Mesh Gradient Overlay */}
      <div className="absolute inset-0 bg-[url('/mesh-gradient.png')] bg-cover opacity-20 mix-blend-overlay" />

      {/* Subtle Grid Pattern */}
      <div className="absolute inset-0 opacity-[0.02] dark:opacity-[0.05]"
           style={{
             backgroundImage: `
               linear-gradient(rgba(59, 130, 246, 0.5) 1px, transparent 1px),
               linear-gradient(90deg, rgba(59, 130, 246, 0.5) 1px, transparent 1px)
             `,
             backgroundSize: '50px 50px'
           }} />

      {/* Glass Card Container */}
      <div className="container relative z-10 px-4 sm:px-6 lg:px-8">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 20 }}
          transition={{ duration: 0.8 }}
          className="max-w-4xl mx-auto backdrop-blur-sm bg-white/30 dark:bg-slate-900/30 p-8 sm:p-12 rounded-3xl border border-white/20 dark:border-slate-700/20 shadow-xl"
        >
          {/* Welcome Badge */}
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 10 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="flex justify-center"
          >
            <Badge variant="secondary" className="mb-6 px-4 py-2 text-sm font-medium backdrop-blur-sm bg-blue-500/10 dark:bg-blue-500/20 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-800">
              {t.hero.welcome}
            </Badge>
          </motion.div>

          {/* Main Title */}
          <motion.h1 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 20 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="text-4xl sm:text-5xl lg:text-7xl font-bold mb-6 text-center bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 dark:from-blue-400 dark:via-blue-500 dark:to-blue-600 bg-clip-text text-transparent leading-tight"
          >
            {t.hero.title}
          </motion.h1>

          {/* Subtitle */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 20 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="mb-8"
          >
            <h2 className="text-xl sm:text-2xl lg:text-3xl font-semibold text-amber-600 dark:text-amber-400 mb-4 text-center">
              {t.hero.subtitle}
            </h2>
            <p className="text-lg sm:text-xl text-slate-700 dark:text-slate-300 max-w-3xl mx-auto leading-relaxed text-center">
              {t.hero.description}
            </p>
          </motion.div>

          {/* Feature Icons */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 20 }}
            transition={{ duration: 0.5, delay: 0.5 }}
            className="grid grid-cols-3 gap-4 mb-8 max-w-2xl mx-auto"
          >
            {[
              { icon: <DollarSign className="h-6 w-6" />, text: "Competitive Rates" },
              { icon: <Clock className="h-6 w-6" />, text: "Fast Decisions" },
              { icon: <TrendingUp className="h-6 w-6" />, text: "Business Growth" }
            ].map((feature, index) => (
              <motion.div 
                key={index}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.6 + (index * 0.1) }}
                className="flex flex-col items-center p-4 rounded-xl bg-white/40 dark:bg-slate-800/40 backdrop-blur-sm border border-white/20 dark:border-slate-700/20"
              >
                <div className="p-2 rounded-full bg-blue-100 dark:bg-blue-900/50 text-blue-600 dark:text-blue-400 mb-2">
                  {feature.icon}
                </div>
                <span className="text-sm font-medium text-center">{feature.text}</span>
              </motion.div>
            ))}
          </motion.div>

          {/* CTA Button */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 20 }}
            transition={{ duration: 0.5, delay: 0.7 }}
            className="flex justify-center mb-8"
          >
            <Button 
              size="lg" 
              className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-8 py-6 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
              onClick={scrollToContact}
            >
              {t.hero.cta}
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </motion.div>

          {/* Trust Badge */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 20 }}
            transition={{ duration: 0.5, delay: 0.8 }}
            className="flex items-center justify-center space-x-2 text-slate-600 dark:text-slate-400"
          >
            <CheckCircle className="h-5 w-5 text-green-500" />
            <span className="text-sm font-medium">{t.hero.trustBadge}</span>
          </motion.div>
        </motion.div>
      </div>

      {/* Enhanced Animated Wave */}
      <div className="absolute bottom-0 left-0 right-0 overflow-hidden">
        {/* Multiple wave layers for depth */}
        <svg viewBox="0 0 1440 120" className="w-full h-auto absolute bottom-0">
          <motion.path
            fill="currentColor"
            className="text-background opacity-60"
            d="M0,64L48,69.3C96,75,192,85,288,80C384,75,480,53,576,48C672,43,768,53,864,64C960,75,1056,85,1152,80C1248,75,1344,53,1392,42.7L1440,32L1440,120L1392,120C1344,120,1248,120,1152,120C1056,120,960,120,864,120C768,120,672,120,576,120C480,120,384,120,288,120C192,120,96,120,48,120L0,120Z"
            animate={{
              d: [
                "M0,64L48,69.3C96,75,192,85,288,80C384,75,480,53,576,48C672,43,768,53,864,64C960,75,1056,85,1152,80C1248,75,1344,53,1392,42.7L1440,32L1440,120L1392,120C1344,120,1248,120,1152,120C1056,120,960,120,864,120C768,120,672,120,576,120C480,120,384,120,288,120C192,120,96,120,48,120L0,120Z",
                "M0,32L48,37.3C96,43,192,53,288,58.7C384,64,480,64,576,58.7C672,53,768,43,864,48C960,53,1056,75,1152,80C1248,85,1344,75,1392,69.3L1440,64L1440,120L1392,120C1344,120,1248,120,1152,120C1056,120,960,120,864,120C768,120,672,120,576,120C480,120,384,120,288,120C192,120,96,120,48,120L0,120Z",
                "M0,64L48,69.3C96,75,192,85,288,80C384,75,480,53,576,48C672,43,768,53,864,64C960,75,1056,85,1152,80C1248,75,1344,53,1392,42.7L1440,32L1440,120L1392,120C1344,120,1248,120,1152,120C1056,120,960,120,864,120C768,120,672,120,576,120C480,120,384,120,288,120C192,120,96,120,48,120L0,120Z"
              ]
            }}
            transition={{
              duration: 20,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
        </svg>
        <svg viewBox="0 0 1440 120" className="w-full h-auto absolute bottom-0">
          <motion.path
            fill="currentColor"
            className="text-background"
            d="M0,96L48,90.7C96,85,192,75,288,80C384,85,480,107,576,112C672,117,768,107,864,96C960,85,1056,75,1152,80C1248,85,1344,107,1392,112.7L1440,118L1440,120L1392,120C1344,120,1248,120,1152,120C1056,120,960,120,864,120C768,120,672,120,576,120C480,120,384,120,288,120C192,120,96,120,48,120L0,120Z"
            animate={{
              d: [
                "M0,96L48,90.7C96,85,192,75,288,80C384,85,480,107,576,112C672,117,768,107,864,96C960,85,1056,75,1152,80C1248,85,1344,107,1392,112.7L1440,118L1440,120L1392,120C1344,120,1248,120,1152,120C1056,120,960,120,864,120C768,120,672,120,576,120C480,120,384,120,288,120C192,120,96,120,48,120L0,120Z",
                "M0,118L48,112.7C96,107,192,96,288,90.7C384,85,480,85,576,90.7C672,96,768,107,864,112C960,117,1056,117,1152,112C1248,107,1344,96,1392,90.7L1440,85L1440,120L1392,120C1344,120,1248,120,1152,120C1056,120,960,120,864,120C768,120,672,120,576,120C480,120,384,120,288,120C192,120,96,120,48,120L0,120Z",
                "M0,96L48,90.7C96,85,192,75,288,80C384,85,480,107,576,112C672,117,768,107,864,96C960,85,1056,75,1152,80C1248,85,1344,107,1392,112.7L1440,118L1440,120L1392,120C1344,120,1248,120,1152,120C1056,120,960,120,864,120C768,120,672,120,576,120C480,120,384,120,288,120C192,120,96,120,48,120L0,120Z"
              ]
            }}
            transition={{
              duration: 15,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2
            }}
          />
        </svg>
      </div>
    </section>
  );
}