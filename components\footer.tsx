'use client';

import React from 'react';
import { Separator } from '@/components/ui/separator';
import { MapPin, Phone, Clock } from 'lucide-react';
import { useLanguage } from '@/components/language-provider';

export function Footer() {
  const { t } = useLanguage();

  const footerLinks = [
    { key: 'privacy', href: '#' },
    { key: 'terms', href: '#' },
    { key: 'disclosure', href: '#' },
    { key: 'guidelines', href: '#' },
    { key: 'cookies', href: '#' },
    { key: 'whatsapp', href: '#' }
  ];

  return (
    <footer className="bg-gradient-to-b from-blue-950 to-blue-900 text-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
          {/* Company Info */}
          <div className="lg:col-span-2">
            <div className="flex items-center space-x-2 mb-6">
              <div className="h-10 w-10 rounded-lg bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center">
                <span className="text-white font-bold">MF</span>
              </div>
              <span className="font-bold text-2xl">{t.footer.company.name}</span>
            </div>
            
            <h3 className="text-xl font-semibold mb-4 text-amber-400">
              {t.footer.company.tagline}
            </h3>
            
            <p className="text-blue-100 leading-relaxed mb-8 max-w-2xl">
              {t.footer.company.description}
            </p>

            {/* Contact Info */}
            <div className="space-y-4">
              <h4 className="text-lg font-semibold text-amber-400">
                {t.footer.company.headquarters}
              </h4>
              
              <div className="flex items-start space-x-3">
                <MapPin className="h-5 w-5 text-blue-300 mt-0.5 flex-shrink-0" />
                <span className="text-blue-100">{t.footer.contact.address}</span>
              </div>
              
              <div className="flex items-center space-x-3">
                <Phone className="h-5 w-5 text-blue-300 flex-shrink-0" />
                <a href={`tel:${t.footer.contact.phone}`} className="text-blue-100 hover:text-white transition-colors">
                  {t.footer.contact.phone}
                </a>
              </div>
            </div>
          </div>

          {/* Hours */}
          <div>
            <h4 className="text-lg font-semibold text-amber-400 mb-6 flex items-center">
              <Clock className="h-5 w-5 mr-2" />
              {t.footer.hours.title}
            </h4>
            <div className="space-y-2 text-blue-100">
              <div>{t.footer.hours.schedule.mon}</div>
              <div>{t.footer.hours.schedule.tue}</div>
              <div>{t.footer.hours.schedule.wed}</div>
              <div>{t.footer.hours.schedule.thu}</div>
              <div>{t.footer.hours.schedule.fri}</div>
              <div>{t.footer.hours.schedule.sat}</div>
              <div>{t.footer.hours.schedule.sun}</div>
            </div>
          </div>
        </div>

        <Separator className="my-12 bg-blue-800" />

        {/* Footer Links */}
        <div className="flex flex-wrap justify-center gap-6 mb-8">
          {footerLinks.map((link) => (
            <a
              key={link.key}
              href={link.href}
              className="text-blue-200 hover:text-white transition-colors text-sm"
            >
              {t.footer.links[link.key as keyof typeof t.footer.links]}
            </a>
          ))}
        </div>

        {/* Copyright */}
        <div className="text-center space-y-2">
          <p className="text-blue-200 text-sm font-medium">
            {t.footer.copyright}
          </p>
          <p className="text-blue-300 text-xs">
            {t.footer.legal}
          </p>
        </div>
      </div>
    </footer>
  );
}